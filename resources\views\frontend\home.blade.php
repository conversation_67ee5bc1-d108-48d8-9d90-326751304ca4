@extends('layouts.app')

@section('title', 'หน้าหลัก - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@section('content')
<!-- Hero Section with <PERSON> Slider -->
<section class="hero-section position-relative {{ $banners->count() === 0 ? 'hero-fallback' : '' }}">
    @if($banners->count() > 0)
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                @foreach($banners as $index => $banner)
                <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                    <div class="banner-slide" style="background-image: url('{{ asset('storage/' . $banner->image_path) }}');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                @endforeach
            </div>

            @if($banners->count() > 1)
            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                @foreach($banners as $index => $banner)
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="{{ $index }}"
                        class="{{ $index === 0 ? 'active' : '' }}" aria-current="true" aria-label="Slide {{ $index + 1 }}"></button>
                @endforeach
            </div>
            @endif
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="row align-items-center min-vh-75">
                    <div class="col-lg-6 mb-5 mb-lg-0">
                        <div class="hero-content">
                            <h1 class="display-4 mb-4 text-white">{{ $settings['site_name'] ?? 'ผู้ใหญ่ประจักร์ เซอร์วิส ช็อป' }}</h1>
                            <p class="lead mb-5 text-white">{{ $settings['site_description'] ?? 'บริการที่ดีที่สุด ด้วยความใส่ใจและคุณภาพ' }}</p>
                            <div class="d-flex flex-column flex-sm-row gap-3">
                                <a href="{{ route('services') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-right me-2"></i>ดูบริการของเรา
                                </a>
                                <a href="{{ route('contact') }}" class="btn btn-outline-light btn-lg">
                                    <i class="fas fa-phone me-2"></i>ติดต่อเรา
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="hero-image text-center">
                            <!-- โลโก้หลักในหน้าแรก - ใช้ไฟล์ โลโก้ผู้ใหญ่ประจักษ์นกสีดำ.png -->
                            <!-- สำหรับเปลี่ยนโลโก้: ใส่รูปใหม่ในโฟลเดอร์ public/images/ และตั้งชื่อเป็น โลโก้ผู้ใหญ่ประจักษ์นกสีดำ.png -->
                            <div class="hero-icon-container">
                                <img src="{{ asset('images/โลโก้ผู้ใหญ่ประจักษ์นกสีดำ.png') }}" alt="โลโก้หลัก " class="img-fluid hero-logo" style="max-height: 300px; width: auto; filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ - แสดงพื้นหลังสีเทาเหมือนเดิม -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="row align-items-center min-vh-75">
                    <div class="col-lg-6 mb-5 mb-lg-0">
                        <div class="hero-content">
                            <h1 class="display-4 mb-4">{{ $settings['site_name'] ?? 'ผู้ใหญ่ประจักร์ เซอร์วิส ช็อป' }}</h1>
                            <p class="lead mb-5">{{ $settings['site_description'] ?? 'บริการที่ดีที่สุด ด้วยความใส่ใจและคุณภาพ' }}</p>
                            <div class="d-flex flex-column flex-sm-row gap-3">
                                <a href="{{ route('services') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-right me-2"></i>ดูบริการของเรา
                                </a>
                                <a href="{{ route('contact') }}" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-phone me-2"></i>ติดต่อเรา
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="hero-image text-center">
                            <!-- โลโก้หลักในหน้าแรก (กรณีไม่มีแบนเนอร์) - ใช้ไฟล์ image (2).png -->
                            <!-- สำหรับเปลี่ยนโลโก้: ใส่รูปใหม่ในโฟลเดอร์ public/images/ และตั้งชื่อเป็น image (2).png -->
                            <div class="hero-icon-container">
                                <img src="{{ asset('images/bridxx (2).png') }}" alt="โลโก้หลัก - สัญลักษณ์ของคุณภาพและความเป็นเลิศ" class="img-fluid hero-logo" style="max-height: 300px; width: auto; filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</section>

<!-- Services Section - รูปแบบเหมือนหน้าบริการ -->
@if($services->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">บริการของเรา</h2>
            <p class="section-subtitle">บริการที่ครอบคลุมและมีคุณภาพ พร้อมดูแลคุณด้วยความใส่ใจ</p>
        </div>

        <div class="row g-4">
            @foreach($services as $service)
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100 shadow-sm">
                    <div class="card-image-container img-size-large position-relative">
                        @php
                            $coverImage = $service->images->where('is_cover', true)->first() ?? $service->images->first();
                            $imagePath = $coverImage ? $coverImage->image_path : $service->image;
                        @endphp

                        @if($imagePath && file_exists(storage_path('app/public/' . $imagePath)))
                        <img src="{{ asset('storage/' . $imagePath) }}"
                             class="img-fit-contain service-image"
                             alt="{{ $service->title }}"
                             style="cursor: pointer;"
                             onclick="window.location.href='{{ route('services.show', $service->id) }}'">
                        @else
                        <img src="{{ asset('images/placeholder.svg') }}"
                             class="img-fit-contain service-image"
                             alt="ไม่มีรูปภาพ"
                             style="cursor: pointer;"
                             onclick="window.location.href='{{ route('services.show', $service->id) }}'">
                        @endif

                        <!-- Gallery indicator -->
                        @if($service->images->count() > 1)
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-dark bg-opacity-75">
                                <i class="fas fa-images me-1"></i>{{ $service->images->count() }}
                            </span>
                        </div>
                        @endif

                        <!-- Hover overlay -->
                        <div class="card-hover-overlay">
                            <div class="text-center">
                                <i class="fas fa-eye fa-2x text-white mb-2"></i>
                                <p class="text-white mb-0">คลิกเพื่อดูรายละเอียด</p>
                            </div>
                        </div>
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <a href="{{ route('services.show', $service->id) }}" class="text-decoration-none text-dark">
                                {{ $service->title }}
                            </a>
                        </h5>
                        <p class="card-text flex-grow-1">{{ $service->description }}</p>

                        @if($service->details)
                        <div class="mb-3">
                            <h6 class="text-muted">รายละเอียดเพิ่มเติม:</h6>
                            <p class="small text-muted">{{ Str::limit($service->details, 120) }}</p>
                        </div>
                        @endif

                        <!-- Service features/highlights -->
                        @if($service->images->count() > 0)
                        <div class="mb-3">
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>มีแกลเลอรี่รูปภาพ {{ $service->images->count() }} รูป
                            </small>
                        </div>
                        @endif

                        <div class="mt-auto">
                            <div class="d-grid gap-2">
                                <a href="{{ route('services.show', $service->id) }}"
                                   class="btn btn-outline-primary btn-hover-effect">
                                    <i class="fas fa-eye me-2"></i>ดูรายละเอียดและแกลเลอรี่
                                </a>
                                <a href="{{ route('contact') }}"
                                   class="btn btn-primary btn-hover-effect">
                                    <i class="fas fa-envelope me-2"></i>ติดต่อสอบถาม
                                </a>
                            </div>
                            <small class="text-muted d-block text-center mt-2">
                                <i class=""></i>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <div class="text-center mt-5">
            <a href="{{ route('services') }}" class="btn btn-primary btn-lg">ดูบริการทั้งหมด</a>
        </div>
    </div>
</section>
@endif



<!-- Activities Section - แกลเลอรี่สวยงาม -->
@if($activities->count() > 0)
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">ผลงานของเรา</h2>
            <p class="section-subtitle">ภาพบรรยากาศการให้บริการที่ผ่านมา แสดงถึงคุณภาพและความใส่ใจ</p>
        </div>

        <div class="row g-4">
            @foreach($activities as $activity)
            <div class="col-md-6 col-lg-3">
                <div class="card service-card h-100 activity-card-home" style="cursor: pointer;">
                    <div class="card-image-container img-size-medium">
                        @php
                            $homeCoverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                            $homeCoverImagePath = $homeCoverImage ? $homeCoverImage->image_path : $activity->image;
                        @endphp
                        @if($homeCoverImagePath && file_exists(storage_path('app/public/' . $homeCoverImagePath)))
                        <img src="{{ asset('storage/' . $homeCoverImagePath) }}"
                             class="img-fit-contain"
                             alt="{{ $activity->title }}">
                        @else
                        <img src="{{ asset('images/placeholder.svg') }}"
                             class="img-fit-contain"
                             alt="ไม่มีรูปภาพ">
                        @endif
                        <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25 d-flex align-items-center justify-content-center opacity-0 home-activity-overlay">
                            <div class="text-center text-white">
                                <i class="fas fa-eye fa-lg mb-1"></i>
                                <div class="small">ดูรายละเอียด</div>
                                @if($activity->images->count() > 1)
                                <div class="small">{{ $activity->images->count() }} รูป</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <h6 class="card-title">{{ $activity->title }}</h6>
                        <p class="card-text small">{{ Str::limit($activity->description, 80) }}</p>
                        <div class="d-flex align-items-center text-muted small">
                            <i class="fas fa-calendar me-2"></i>
                            {{ $activity->activity_date->format('d/m/Y') }}
                        </div>
                    </div>
                    <a href="{{ route('activities.show', $activity->id) }}" class="stretched-link"></a>
                </div>
            </div>
            @endforeach
        </div>

        <div class="text-center mt-5">
            <a href="{{ route('activities') }}" class="btn btn-primary btn-lg">ดูผลงานทั้งหมด</a>
        </div>
    </div>
</section>
@endif

<!-- Contact CTA Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอดเวลา</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการสแตนบาย 24 ชั่วโมง
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<style>
/* Service Card Enhancements for Home Page */
.service-card {
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

.service-card .card-body {
    position: relative;
    z-index: 5; /* Ensure card body is above overlay */
}

.service-image {
    transition: transform 0.3s ease;
}

.service-card:hover .service-image {
    transform: scale(1.05);
}

.card-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none; /* Allow clicks to pass through */
}

.service-card:hover .card-hover-overlay {
    opacity: 1;
}

.btn-hover-effect {
    transition: all 0.3s ease;
    position: relative;
    z-index: 10; /* Ensure buttons are above overlay */
}

.btn-hover-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-title a:hover {
    color: #2c3e50 !important;
}

/* Gallery badge animation */
.badge {
    transition: all 0.3s ease;
}

.service-card:hover .badge {
    transform: scale(1.1);
}

/* Loading animation for images */
.service-image {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.service-image[src] {
    background: none;
    animation: none;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Activity card hover effects */
.home-activity-overlay {
    transition: opacity 0.3s ease;
}

.activity-card-home:hover .home-activity-overlay {
    opacity: 1 !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .service-card:hover {
        transform: none;
    }

    .card-hover-overlay {
        display: none;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add loading state to images
    const images = document.querySelectorAll('.service-image');

    images.forEach(img => {
        img.addEventListener('load', function() {
            this.classList.add('loaded');
        });

        img.addEventListener('error', function() {
            this.src = '{{ asset("images/placeholder.svg") }}';
            this.alt = 'ไม่สามารถโหลดรูปภาพได้';
        });
    });

    // Service card click functionality
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on the button
            if (e.target.closest('.btn')) {
                return;
            }

            const link = card.querySelector('a[href*="services"]');
            if (link) {
                window.location.href = link.href;
            }
        });
    });

    // Activity card click functionality (existing)
    const activityCards = document.querySelectorAll('.activity-card-home');
    activityCards.forEach(card => {
        card.addEventListener('click', function(e) {
            if (e.target.closest('.btn')) {
                return;
            }

            const link = card.querySelector('.stretched-link');
            if (link) {
                window.location.href = link.href;
            }
        });
    });
});
</script>
@endsection
